/**
 * Utility functions for processing MLS data and mapping it to form fields
 */

import { getFormMapping } from './MlsFieldMappingTemplate';

/**
 * Default field mapping for common MLS data fields to PDF form field names
 * This is used as fallback when no specific form mapping is found
 */
export const defaultMlsFieldMapping = {
  // Address
  address: "address",

  // Property Details
  county: "county",
  subdivisionName: "subdivision",
  yearBuilt: "yearBuilt",
  parcelNumber: "parcelNumber",
  legalDescription: "legalDescription",
  inclusions: "inclusions",
  exclusions: "exclusions",
  metroDistrictUrl: "metroDistrictUrl",
  emHolder: "earnestMoneyHolder",

  // Custom Fields
  titleCo: "titleCompany",
  emDepositAmount: "earnestMoneyAmount",
  associationFee: "associationFee",
  associationFeeFreq: "associationFeeFrequency",
  ownerName: "ownerName",
};

/**
 * Get the field mapping for a specific form
 * @param {string} formTitle - The title of the form
 * @returns {object} Field mapping object
 */
export function getFieldMappingForForm(formTitle) {
  // First try to get form-specific mapping from template
  const formMapping = getFormMapping(formTitle);
  if (formMapping) {
    return formMapping;
  }

  // Fall back to default mapping
  return defaultMlsFieldMapping;
}

/**
 * Process MLS data and map it to form field values
 * @param {object} mlsData - The MLS data object
 * @param {object} selectedOptions - Object indicating which fields to import
 * @param {string} formTitle - The title of the form being filled
 * @returns {object} Mapped form field values
 */
export function processMlsDataForForm(mlsData, selectedOptions, formTitle) {
  const fieldMapping = getFieldMappingForForm(formTitle);
  const formFieldValues = {};
  
  // Process each selected option
  Object.keys(selectedOptions).forEach(optionKey => {
    if (!selectedOptions[optionKey]) return; // Skip if not selected
    
    let value = null;
    
    // Extract value from MLS data based on option key
    switch (optionKey) {
      // Address
      case 'address':
        if (mlsData.address) {
          value = `${mlsData.address.street}${mlsData.address.unit ? ` Unit ${mlsData.address.unit}` : ''}, ${mlsData.address.city}, ${mlsData.address.state} ${mlsData.address.zip}`;
        }
        break;
      // Property Details
      case 'county':
        value = mlsData.propertyDetails?.county;
        break;
      case 'subdivisionName':
        value = mlsData.propertyDetails?.subdivisionName;
        break;
      case 'yearBuilt':
        value = mlsData.propertyDetails?.yearBuilt;
        break;
      case 'parcelNumber':
        value = mlsData.propertyDetails?.parcelNumber;
        break;
      case 'legalDescription':
        value = mlsData.propertyDetails?.legalDescription;
        break;
      case 'inclusions':
        value = mlsData.propertyDetails?.inclusions;
        break;
      case 'exclusions':
        value = mlsData.propertyDetails?.exclusions;
        break;
      case 'metroDistrictUrl':
        value = mlsData.propertyDetails?.metroDistrictUrl;
        break;
      case 'emHolder':
        value = mlsData.propertyDetails?.earnestMoneyHolder;
        break;
      // Custom Fields
      case 'titleCo':
        value = mlsData.customFields?.titleCo;
        break;
      case 'emDepositAmount':
        value = mlsData.customFields?.emDepositAmount;
        break;
      case 'associationFee':
        value = mlsData.customFields?.associationFee;
        break;
      case 'associationFeeFreq':
        value = mlsData.customFields?.associationFeeFreq;
        break;
      case 'ownerName':
        value = mlsData.customFields?.ownerName;
        break;
      case 'colistingAgent':
        value = mlsData.colistingAgent;
        break;
      case 'listingBrokerageFirm':
        value = mlsData.listingAgent?.brokerageName;
        break;
      case 'listingBrokerLicenseNumber':
        value = mlsData.listingAgent?.brokerLicenseNumber;
        break;
      case 'listingBrokerEmail':
        value = mlsData.listingAgent?.email;
        break;
      case 'listingBrokerPhone':
        value = mlsData.listingAgent?.phone;
        break;
      case 'listingBrokerAddressStreet':
        value = mlsData.listingAgent?.address?.street;
        break;
      case 'listingBrokerAddressCity':
        value = mlsData.listingAgent?.address?.city;
        break;
      case 'listingBrokerAddressState':
        value = mlsData.listingAgent?.address?.state;
        break;
      case 'listingBrokerAddressZip':
        value = mlsData.listingAgent?.address?.zipcode;
        break;
      case 'listingBrokerName':
        value = mlsData.listingAgent?.firstName + " " + mlsData.listingAgent?.lastName;
        break;
      case 'listingBrokerAddressCityStateZip': 
        value = (mlsData.listingAgent?.address?.city || "") + ", " + (mlsData.listingAgent?.address?.state || "") + " " + (mlsData.listingAgent?.address?.zipcode || "");
        break;
      default:
        // Handle any additional fields
        value = mlsData[optionKey] || mlsData.propertyDetails?.[optionKey] || mlsData.customFields?.[optionKey];
    }
    
    // Map to form field name if value exists
    if (value && fieldMapping[optionKey]) {
      formFieldValues[fieldMapping[optionKey]] = value;
    }
  });
  
  return formFieldValues;
}

/**
 * Get available MLS fields for import based on the MLS data
 * @param {object} mlsData - The MLS data object
 * @returns {object} Object with field keys and their availability/values
 */
export function getAvailableMlsFields(mlsData) {
  const availableFields = {};

  // Check address
  if (mlsData.address) {
    availableFields.address = {
      label: "Property Address",
      value: `${mlsData.address.street}${mlsData.address.unit ? ` Unit ${mlsData.address.unit}` : ''}, ${mlsData.address.city}, ${mlsData.address.state} ${mlsData.address.zip}`,
      category: "Address"
    };
  }

  // Check property details
  if (mlsData.propertyDetails?.county) {
    availableFields.county = {
      label: "County",
      value: mlsData.propertyDetails.county,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.subdivisionName) {
    availableFields.subdivisionName = {
      label: "Subdivision",
      value: mlsData.propertyDetails.subdivisionName,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.yearBuilt) {
    availableFields.yearBuilt = {
      label: "Year Built",
      value: mlsData.propertyDetails.yearBuilt,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.parcelNumber) {
    availableFields.parcelNumber = {
      label: "Parcel Number",
      value: mlsData.propertyDetails.parcelNumber,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.legalDescription) {
    availableFields.legalDescription = {
      label: "Legal Description",
      value: mlsData.propertyDetails.legalDescription,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.inclusions) {
    availableFields.inclusions = {
      label: "Inclusions",
      value: mlsData.propertyDetails.inclusions,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.exclusions) {
    availableFields.exclusions = {
      label: "Exclusions",
      value: mlsData.propertyDetails.exclusions,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.metroDistrictUrl) {
    availableFields.metroDistrictUrl = {
      label: "Metro District URL",
      value: mlsData.propertyDetails.metroDistrictUrl,
      category: "Property Details"
    };
  }

  if (mlsData.propertyDetails?.earnestMoneyHolder) {
    availableFields.emHolder = {
      label: "Earnest Money Holder",
      value: mlsData.propertyDetails.earnestMoneyHolder,
      category: "Property Details"
    };
  }

  // Check custom fields
  if (mlsData.customFields?.titleCo) {
    availableFields.titleCo = {
      label: "Title Company",
      value: mlsData.customFields.titleCo,
      category: "Additional Fields"
    };
  }

  if (mlsData.customFields?.emDepositAmount) {
    availableFields.emDepositAmount = {
      label: "EM Deposit Amount",
      value: mlsData.customFields.emDepositAmount,
      category: "Additional Fields"
    };
  }

  if (mlsData.customFields?.associationFee) {
    availableFields.associationFee = {
      label: "Association Fee",
      value: mlsData.customFields.associationFee,
      category: "Additional Fields"
    };
  }

  if (mlsData.customFields?.associationFeeFreq) {
    availableFields.associationFeeFreq = {
      label: "Association Fee Frequency",
      value: mlsData.customFields.associationFeeFreq,
      category: "Additional Fields"
    };
  }

  if (mlsData.customFields?.ownerName) {
    availableFields.ownerName = {
      label: "Owner Name",
      value: mlsData.customFields.ownerName,
      category: "Additional Fields"
    };
  }

  return availableFields;
}

/**
 * Validate that the form supports MLS import
 * @param {object} doc - The document object
 * @returns {boolean} Whether the form supports MLS import
 */
export function canFormImportFromMls(doc) {
  return doc && doc.canImportFromListing === true;
}

/**
 * Get the form field data structure for a given form
 * This is used to understand the available fields in the PDF
 * @param {string} formTitle - The title of the form
 * @param {object} transaction - The transaction object
 * @returns {array} Array of form field definitions
 */
export function getFormFieldStructure(formTitle, transaction) {
  // This would import and call the getFormFieldData function
  // For now, return empty array as placeholder
  try {
    const { getFormFieldData } = require('./formFieldData/formFieldData');
    return getFormFieldData(formTitle, transaction);
  } catch (error) {
    console.warn('Could not load form field data:', error);
    return [];
  }
}

/**
 * Find matching form fields for MLS data
 * @param {array} formFields - Array of form field definitions
 * @param {string} mlsFieldKey - The MLS field key to match
 * @returns {array} Array of matching form field names
 */
export function findMatchingFormFields(formFields, mlsFieldKey) {
  const searchTerms = getSearchTermsForMlsField(mlsFieldKey);
  const matchingFields = [];
  
  formFields.forEach(field => {
    if (field.name) {
      const fieldNameLower = field.name.toLowerCase();
      searchTerms.forEach(term => {
        if (fieldNameLower.includes(term.toLowerCase())) {
          matchingFields.push(field.name);
        }
      });
    }
  });
  
  return [...new Set(matchingFields)]; // Remove duplicates
}

/**
 * Get search terms for a given MLS field key
 * @param {string} mlsFieldKey - The MLS field key
 * @returns {array} Array of search terms
 */
function getSearchTermsForMlsField(mlsFieldKey) {
  const searchTermsMap = {
    inclusions: ['inclusion', 'include', 'included'],
    exclusions: ['exclusion', 'exclude', 'excluded'],
    metroDistrictUrl: ['metro', 'district', 'url', 'website'],
    emHolder: ['earnest', 'money', 'holder', 'em holder'],
    titleCo: ['title', 'company', 'title co'],
    emDepositAmount: ['earnest', 'deposit', 'amount', 'em deposit'],
    associationFee: ['association', 'fee', 'hoa'],
    associationFeeFreq: ['association', 'frequency', 'freq', 'hoa freq'],
    ownerName: ['owner', 'seller', 'name'],
  };
  
  return searchTermsMap[mlsFieldKey] || [mlsFieldKey];
}
