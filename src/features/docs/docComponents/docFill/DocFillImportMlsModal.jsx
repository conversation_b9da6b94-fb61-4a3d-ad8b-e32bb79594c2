import React, { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
import { Button, Grid, Image, Message, Header, Divider } from "semantic-ui-react";
import MyTextInput from "../../../../app/common/form/MyTextInput";
import {
  functionFetchMlsCoCren,
  functionFetchMlsDataFiniti,
  functionFetchMlsIres,
} from "../../../../app/firestore/functionsService";
import { Formik, Form, useFormikContext } from "formik";
import { useSelector, useDispatch } from "react-redux";
import stateNameToAbbreviation, {
  convertAddressFull,
} from "../../../../app/common/util/util";
import MyCheckbox from "../../../../app/common/form/MyCheckbox";
import MySelectInput from "../../../../app/common/form/MySelectInput";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../../app/common/modals/modalSlice";
import { updateDocInDb } from "../../../../app/firestore/firestoreService";
import {
  processMlsDataForForm,
  getAvailableMlsFields,
  canFormImportFromMls
} from "./MlsImportUtils";

export default function DocFillImportMlsModal() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { transaction } = useSelector((state) => state.transaction);
  const { doc } = useSelector((state) => state.doc);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  
  const [importMlsStatus, setImportMlsStatus] = useState("Initial");
  const [mlsData, setMlsData] = useState(null);
  const [mlsId, setMlsId] = useState("");

  const agentState = currentUserProfile?.state || "Colorado";

  let mlsOptions = [];
  if (currentUserProfile?.mlsAccess?.length > 0) {
    currentUserProfile.mlsAccess.map((oneMls) => {
      if (oneMls?.mlsIdCode) {
        mlsOptions.push({
          key: oneMls.mlsIdCode,
          value: oneMls.mlsIdCode,
          text: oneMls.mlsName || "MLS",
        });
      }
      return true;
    });
  }

  function mlsImportSuccess(formattedData) {
    setImportMlsStatus("Complete");
    setMlsData(formattedData);
    toast.success("MLS data imported successfully");
  }

  async function handleMlsImport(values) {
    if (mlsId) {
      try {
        setImportMlsStatus("Loading");
        let mlsValues = null;

        // Try different MLS sources
        if (values.mlsIdCode === "CO_CREN") {
          mlsValues = await functionFetchMlsCoCren(mlsId);
        } else if (values.mlsIdCode === "CO_IRES") {
          mlsValues = await functionFetchMlsIres(mlsId);
        }
        // if (currentUserProfile.mlsAccess?.[0]?.mlsIdCode === "CREN") {
        //   mlsValues = await functionFetchMlsCoCren({
        //     mlsId: mlsId,
        //     mlsIdCode: values.mlsIdCode || currentUserProfile.mlsAccess[0].mlsIdCode,
        //   });
        // } else if (currentUserProfile.mlsAccess?.[0]?.mlsIdCode === "IRES") {
        //   mlsValues = await functionFetchMlsIres({
        //     mlsId: mlsId,
        //     mlsIdCode: values.mlsIdCode || currentUserProfile.mlsAccess[0].mlsIdCode,
        //   });
        // }

        if (mlsValues) {
          mlsImportSuccess(mlsValues);
        } else {
          // Fallback to DataFiniti
          mlsValues = await functionFetchMlsDataFiniti({
            mlsId: mlsId,
            state: agentState,
          });
          if (mlsValues) {
            mlsImportSuccess(mlsValues);
          } else {
            setImportMlsStatus("Error");
            toast.error(
              `Error when importing MLS data. Please check the MLS ID and try again.`
            );
          }
        }
      } catch (error) {
        setImportMlsStatus("Error");
        toast.error(
          `Error when importing MLS data. Please check the MLS ID and try again.`
        );
      }
    } else {
      toast.error("Please add an MLS number");
    }
  }

  function WatcherMlsNum() {
    const { values } = useFormikContext();
    useEffect(() => {
      if (values.mlsNumbers && values.mlsNumbers[0]) {
        setMlsId(values.mlsNumbers[0]);
      }
    }, [values.mlsNumbers]);
    return null;
  }

  const initialValues = {
    mlsIdCode: currentUserProfile.mlsAccess?.[0]?.mlsIdCode || "",
    mlsNumbers: [""],
    mlsDataOptions: {
      useMlsPic: true,
      addListingAgentToParties: true,
    },
    importOptions: {
      // Address fields
      address: true,
      // Property details
      county: true,
      subdivisionName: true,
      yearBuilt: true,
      parcelNumber: true,
      legalDescription: true,
      // Required fields from user
      inclusions: true,
      exclusions: true,
      metroDistrictUrl: true,
      emHolder: true,
      titleCo: true,
      emDepositAmount: true,
      associationFee: true,
      associationFeeFreq: true,
      ownerName: true,
    },
  };

  async function handleImportToForm(values) {
    if (!mlsData) {
      toast.error("Please import MLS data first");
      return;
    }

    try {
      // Use the utility function to process MLS data and map to form fields
      const formFieldValues = processMlsDataForForm(
        mlsData,
        values.importOptions,
        doc.title || doc.name
      );

      // Update the document with the mapped form field values
      const formFieldUpdates = {};
      Object.keys(formFieldValues).forEach(fieldName => {
        formFieldUpdates[`formFieldValues.${fieldName}`] = formFieldValues[fieldName];
      });

      // Handle MLS picture import
      if (values.mlsDataOptions?.useMlsPic && mlsData.pic) {
        // Note: Picture import would need additional implementation
        // to handle file upload and storage
        console.log("Picture import selected:", mlsData.pic);
      }

      // Handle listing agent import
      if (values.mlsDataOptions?.addListingAgentToParties && mlsData.listingAgent) {
        // Note: Adding listing agent to parties would need additional implementation
        // to integrate with the parties/people management system
        console.log("Listing agent import selected:", mlsData.listingAgent);
      }

      if (Object.keys(formFieldUpdates).length === 0) {
        toast.warning("No fields were selected or mapped for import");
        return;
      }

      await updateDocInDb(doc.id, formFieldUpdates);

      let successMessage = `MLS data imported to form successfully (${Object.keys(formFieldUpdates).length} fields updated)`;

      if (values.mlsDataOptions?.useMlsPic && mlsData.pic) {
        successMessage += ". Picture import requires additional setup.";
      }

      if (values.mlsDataOptions?.addListingAgentToParties && mlsData.listingAgent) {
        successMessage += ". Listing agent import requires additional setup.";
      }

      toast.success(successMessage);
      dispatch(closeModal({ modalType: "DocFillImportMlsModal" }));

      // Refresh the page to show updated form
      setTimeout(() => {
        window.location.reload();
      }, 500);

    } catch (error) {
      toast.error("Error importing data to form");
      console.error(error);
    }
  }

  return (
    <ModalWrapper size="large">
      <div style={{ padding: "20px" }}>
        <Header size="large" color="blue">
          Import MLS Data
        </Header>
        <Divider />

        <Formik initialValues={initialValues} onSubmit={handleImportToForm}>
          {({ values, setFieldValue }) => (
            <Form>
              <Grid>
                <Grid.Row>
                  <Grid.Column
                    mobile={16}
                    computer={4}
                    className="small padding top"
                  >
                    {currentUserProfile.mlsAccess?.[0]?.mlsName && (
                      <MySelectInput
                        key="mlsIdCode"
                        name="mlsIdCode"
                        label="MLS Name"
                        options={mlsOptions}
                      />
                    )}
                  </Grid.Column>
                  <Grid.Column
                    mobile={16}
                    computer={4}
                    className="small padding top"
                  >
                    <WatcherMlsNum />
                    <MyTextInput
                      name="mlsNumbers[0]"
                      label="MLS #"
                      value={values.mlsNumbers?.[0] || ""}
                    />
                  </Grid.Column>
                  <Grid.Column
                    mobile={16}
                    computer={4}
                    className="small padding top"
                  >
                    <Button
                      primary
                      loading={importMlsStatus === "Loading"}
                      disabled={
                        importMlsStatus === "Loading" ||
                        importMlsStatus === "Complete"
                      }
                      className={isMobile ? "fluid" : null}
                      style={{ marginTop: "20px" }}
                      onClick={(e) => {
                        e.preventDefault();
                        handleMlsImport(values);
                      }}
                    >
                      {importMlsStatus === "Initial" ||
                      importMlsStatus === "Error"
                        ? "Import MLS Details"
                        : `Import ${importMlsStatus}`}
                    </Button>
                  </Grid.Column>
                </Grid.Row>

                {!currentUserProfile.mlsAccess &&
                  importMlsStatus === "Error" && (
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <Message negative>
                          <Message.Header>
                            You do not have access to any MLS, and we could not
                            find that MLS number on the internet for your area.
                          </Message.Header>
                          <p>
                            Please contact your administrator to add MLS access
                            for your account, or try again with a different MLS
                            number.
                          </p>
                        </Message>
                      </Grid.Column>
                    </Grid.Row>
                  )}

                {mlsData && (
                  <>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <Header as="h3" color="blue">
                          MLS Data Retrieved
                        </Header>
                        <p>
                          Select which data you want to import into the form:
                        </p>
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row>
                      {mlsData?.pic && (
                        <Grid.Column mobile={16} computer={4}>
                          <div className="small bottom margin">
                            <Image
                              bordered
                              rounded
                              size="medium"
                              wrapped
                              src={mlsData.pic}
                            />
                            <br />
                            <MyCheckbox
                              name="mlsDataOptions.useMlsPic"
                              label="Import Picture to Transaction"
                            />
                          </div>
                        </Grid.Column>
                      )}
                      {mlsData?.address && (
                        <Grid.Column mobile={16} computer={4}>
                          <div className="small bottom margin">
                            <>
                              <h4>Property Address</h4>
                              <MyCheckbox
                                name="importOptions.address"
                                label="Property Address"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.address.street}
                                {mlsData.address.unit &&
                                  ` Unit ${mlsData.address.unit}`}
                                <br />
                                {mlsData.address.city}, {mlsData.address.state}{" "}
                                {mlsData.address.zip}
                              </p>
                            </>

                            {mlsData.propertyDetails.county && (
                              <>
                                <MyCheckbox
                                  name="importOptions.county"
                                  label="County"
                                />
                                <p
                                  className="mini bottom margin"
                                  style={{ marginLeft: "20px", color: "#666" }}
                                >
                                  {mlsData.propertyDetails.county}
                                </p>
                              </>
                            )}
                            {mlsData.propertyDetails?.yearBuilt && (
                              <div>
                                <MyCheckbox
                                  name="importOptions.yearBuilt"
                                  label="Year Built"
                                />
                                <p
                                  className="mini bottom margin"
                                  style={{ marginLeft: "20px", color: "#666" }}
                                >
                                  {mlsData.propertyDetails.yearBuilt}
                                </p>
                              </div>
                            )}
                          </div>
                        </Grid.Column>
                      )}

                      <Grid.Column mobile={16} computer={5}>
                        <div>
                          {mlsData?.propertyDetails?.legalDescription && (
                            <>
                              <h4>Legal Description</h4>
                              <MyCheckbox
                                name="importOptions.legalDescription"
                                label="Legal Description"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.legalDescription}
                              </p>
                            </>
                          )}
                          {mlsData.propertyDetails?.subdivisionName && (
                            <div>
                              <MyCheckbox
                                name="importOptions.subdivisionName"
                                label="Subdivision"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.subdivisionName}
                              </p>
                            </div>
                          )}
                        </div>
                      </Grid.Column>
                    </Grid.Row>

                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <div className="small bottom margin">
                          <h4>Property Details</h4>

                          {/* {mlsData.propertyDetails?.parcelNumber && (
                            <div>
                              <MyCheckbox
                                name="importOptions.parcelNumber"
                                label="Parcel #"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.parcelNumber}
                              </p>
                            </div>
                          )} */}
                          {/* {mlsData.propertyDetails?.legalDescription && (
                            <div>
                              <MyCheckbox
                                name="importOptions.legalDescription"
                                label="Legal Description"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.legalDescription}
                              </p>
                            </div>
                          )} */}
                          {mlsData.propertyDetails?.inclusions && (
                            <div style={{ width: "100%" }}>
                              <MyCheckbox
                                name="importOptions.inclusions"
                                label="Inclusions"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.inclusions}
                              </p>
                            </div>
                          )}
                          {mlsData.propertyDetails?.exclusions && (
                            <div style={{ width: "100%" }}>
                              <MyCheckbox
                                name="importOptions.exclusions"
                                label="Exclusions"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.exclusions}
                              </p>
                            </div>
                          )}
                          {mlsData.propertyDetails?.metroDistrictWebsite && (
                            <div style={{ width: "100%" }}>
                              <MyCheckbox
                                name="importOptions.metroDistrictUrl"
                                label="Metro District URL"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.metroDistrictWebsite}
                              </p>
                            </div>
                          )}
                          {mlsData.propertyDetails?.earnestMoneyHolder && (
                            <div style={{ width: "100%" }}>
                              <MyCheckbox
                                name="importOptions.emHolder"
                                label="Earnest Money Holder"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.propertyDetails.earnestMoneyHolder}
                              </p>
                            </div>
                          )}
                        </div>
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row>
                      {/* Listing Agent Section */}
                      <Grid.Column mobile={16} computer={4}>
                        <div className="small bottom margin">
                          <h4>Listing Agent</h4>
                          <MyCheckbox
                            name="mlsDataOptions.addListingAgentToParties"
                            label="Add Listing Agent to Parties"
                          />
                          {mlsData.listingAgent && (
                            <>
                              <p className="mini bottom margin">
                                Name: {mlsData.listingAgent.firstName}{" "}
                                {mlsData.listingAgent.lastName}
                              </p>
                              {mlsData.listingAgent.email && (
                                <p className="mini bottom margin">
                                  Email: {mlsData.listingAgent.email}
                                </p>
                              )}
                              {mlsData.listingAgent.phone && (
                                <p className="mini bottom margin">
                                  Phone: {mlsData.listingAgent.phone}
                                </p>
                              )}
                              {mlsData.listingAgent.brokerLicenseNumber && (
                                <p className="mini bottom margin">
                                  License #:{" "}
                                  {mlsData.listingAgent.brokerLicenseNumber}
                                </p>
                              )}
                              {mlsData.listingAgent.brokerageName && (
                                <p className="mini bottom margin">
                                  Brokerage:{" "}
                                  {mlsData.listingAgent.brokerageName}
                                </p>
                              )}
                              {mlsData.listingAgent.address?.city && (
                                <p className="mini bottom margin">
                                  Address:{" "}
                                  {convertAddressFull(
                                    mlsData.listingAgent.address
                                  )}
                                </p>
                              )}
                              {!mlsData.listingAgent.email && (
                                <p className="mini bottom margin bold">
                                  No email found. Manually add LA in Parties.
                                </p>
                              )}
                            </>
                          )}
                        </div>
                      </Grid.Column>

                      {/* Additional/Custom Fields Section */}
                      <Grid.Column mobile={16} computer={4}>
                        <div className="small bottom margin">
                          <h4>Additional Fields</h4>
                          {mlsData.customFields?.titleCo && (
                            <div>
                              {/* <MyCheckbox
                                name="importOptions.titleCo"
                                label="Title Company"
                              /> */}
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.customFields.titleCo}
                              </p>
                            </div>
                          )}
                          {mlsData.customFields?.emDepositAmount && (
                            <div>
                              <MyCheckbox
                                name="importOptions.emDepositAmount"
                                label="EM Deposit Amount"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.customFields.emDepositAmount}
                              </p>
                            </div>
                          )}
                          {mlsData.customFields?.associationFee && (
                            <div>
                              <MyCheckbox
                                name="importOptions.associationFee"
                                label="Association Fee"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.customFields.associationFee}
                              </p>
                            </div>
                          )}
                          {mlsData.customFields?.associationFeeFreq && (
                            <div>
                              <MyCheckbox
                                name="importOptions.associationFeeFreq"
                                label="Association Fee Frequency"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.customFields.associationFeeFreq}
                              </p>
                            </div>
                          )}
                          {mlsData.customFields?.ownerName && (
                            <div>
                              <MyCheckbox
                                name="importOptions.ownerName"
                                label="Owner Name"
                              />
                              <p
                                className="mini bottom margin"
                                style={{ marginLeft: "20px", color: "#666" }}
                              >
                                {mlsData.customFields.ownerName}
                              </p>
                            </div>
                          )}
                        </div>
                      </Grid.Column>
                    </Grid.Row>

                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <div style={{ textAlign: "right", marginTop: "20px" }}>
                          <Button
                            onClick={() =>
                              dispatch(
                                closeModal({
                                  modalType: "DocFillImportMlsModal",
                                })
                              )
                            }
                            style={{ marginRight: "10px" }}
                          >
                            Cancel
                          </Button>
                          <Button primary type="submit" disabled={!mlsData}>
                            Import Selected Data
                          </Button>
                        </div>
                      </Grid.Column>
                    </Grid.Row>
                  </>
                )}
              </Grid>
            </Form>
          )}
        </Formik>
      </div>
    </ModalWrapper>
  );
}
