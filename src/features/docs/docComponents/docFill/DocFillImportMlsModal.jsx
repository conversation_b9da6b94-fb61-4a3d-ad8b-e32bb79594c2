import React, { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
import { Button, Grid, Image, Message, Header, Divider } from "semantic-ui-react";
import MyTextInput from "../../../../app/common/form/MyTextInput";
import {
  functionFetchMlsCoCren,
  functionFetchMlsDataFiniti,
  functionFetchMlsIres,
} from "../../../../app/firestore/functionsService";
import { Formik, Form, useFormikContext } from "formik";
import { useSelector, useDispatch } from "react-redux";
import stateNameToAbbreviation, {
  convertAddressFull,
} from "../../../../app/common/util/util";
import MyCheckbox from "../../../../app/common/form/MyCheckbox";
import MySelectInput from "../../../../app/common/form/MySelectInput";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../../app/common/modals/modalSlice";
import { updateDocInDb } from "../../../../app/firestore/firestoreService";
import {
  processMlsDataForForm,
  getAvailableMlsFields,
  canFormImportFromMls
} from "./MlsImportUtils";

export default function DocFillImportMlsModal() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { transaction } = useSelector((state) => state.transaction);
  const { doc } = useSelector((state) => state.doc);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  
  const [importMlsStatus, setImportMlsStatus] = useState("Initial");
  const [mlsData, setMlsData] = useState(null);
  const [mlsId, setMlsId] = useState("");

  const agentState = currentUserProfile?.state || "Colorado";

  let mlsOptions = [];
  if (currentUserProfile?.mlsAccess?.length > 0) {
    currentUserProfile.mlsAccess.map((oneMls) => {
      if (oneMls?.mlsIdCode) {
        mlsOptions.push({
          key: oneMls.mlsIdCode,
          value: oneMls.mlsIdCode,
          text: oneMls.mlsName || "MLS",
        });
      }
      return true;
    });
  }

  function mlsImportSuccess(formattedData) {
    setImportMlsStatus("Complete");
    setMlsData(formattedData);
    toast.success("MLS data imported successfully");
  }

  async function handleMlsImport(values) {
    if (mlsId) {
      try {
        setImportMlsStatus("Loading");
        let mlsValues = null;

        // Try different MLS sources
        if (values.mlsIdCode === "CO_CREN") {
          mlsValues = await functionFetchMlsCoCren(mlsId);
        } else if (values.mlsIdCode === "CO_IRES") {
          mlsValues = await functionFetchMlsIres(mlsId);
        }
        // if (currentUserProfile.mlsAccess?.[0]?.mlsIdCode === "CREN") {
        //   mlsValues = await functionFetchMlsCoCren({
        //     mlsId: mlsId,
        //     mlsIdCode: values.mlsIdCode || currentUserProfile.mlsAccess[0].mlsIdCode,
        //   });
        // } else if (currentUserProfile.mlsAccess?.[0]?.mlsIdCode === "IRES") {
        //   mlsValues = await functionFetchMlsIres({
        //     mlsId: mlsId,
        //     mlsIdCode: values.mlsIdCode || currentUserProfile.mlsAccess[0].mlsIdCode,
        //   });
        // }

        if (mlsValues) {
          mlsImportSuccess(mlsValues);
        } else {
          // Fallback to DataFiniti
          mlsValues = await functionFetchMlsDataFiniti({
            mlsId: mlsId,
            state: agentState,
          });
          if (mlsValues) {
            mlsImportSuccess(mlsValues);
          } else {
            setImportMlsStatus("Error");
            toast.error(
              `Error when importing MLS data. Please check the MLS ID and try again.`
            );
          }
        }
      } catch (error) {
        setImportMlsStatus("Error");
        toast.error(
          `Error when importing MLS data. Please check the MLS ID and try again.`
        );
      }
    } else {
      toast.error("Please add an MLS number");
    }
  }

  function WatcherMlsNum() {
    const { values } = useFormikContext();
    useEffect(() => {
      if (values.mlsNumbers && values.mlsNumbers[0]) {
        setMlsId(values.mlsNumbers[0]);
      }
    }, [values.mlsNumbers]);
    return null;
  }

  const initialValues = {
    mlsIdCode: currentUserProfile.mlsAccess?.[0]?.mlsIdCode || "",
    mlsNumbers: [""],
    importOptions: {
      inclusions: true,
      exclusions: true,
      metroDistrictUrl: true,
      emHolder: true,
      titleCo: true,
      emDepositAmount: true,
      associationFee: true,
      associationFeeFreq: true,
      ownerName: true,
    },
  };

  async function handleImportToForm(values) {
    if (!mlsData) {
      toast.error("Please import MLS data first");
      return;
    }

    try {
      // Use the utility function to process MLS data and map to form fields
      const formFieldValues = processMlsDataForForm(
        mlsData,
        values.importOptions,
        doc.title || doc.name
      );

      // Update the document with the mapped form field values
      const formFieldUpdates = {};
      Object.keys(formFieldValues).forEach(fieldName => {
        formFieldUpdates[`formFieldValues.${fieldName}`] = formFieldValues[fieldName];
      });

      if (Object.keys(formFieldUpdates).length === 0) {
        toast.warning("No fields were selected or mapped for import");
        return;
      }

      await updateDocInDb(doc.id, formFieldUpdates);

      toast.success(`MLS data imported to form successfully (${Object.keys(formFieldUpdates).length} fields updated)`);
      dispatch(closeModal({ modalType: "DocFillImportMlsModal" }));

      // Refresh the page to show updated form
      setTimeout(() => {
        window.location.reload();
      }, 500);

    } catch (error) {
      toast.error("Error importing data to form");
      console.error(error);
    }
  }

  return (
    <ModalWrapper size="large">
      <div style={{ padding: "20px" }}>
        <Header size="large" color="blue">
          Import MLS Data
        </Header>
        <Divider />
        
        <Formik
          initialValues={initialValues}
          onSubmit={handleImportToForm}
        >
          {({ values, setFieldValue }) => (
            <Form>
              <Grid>
                <Grid.Row>
                  <Grid.Column mobile={16} computer={4} className="small padding top">
                    {currentUserProfile.mlsAccess?.[0]?.mlsName && (
                      <MySelectInput
                        key="mlsIdCode"
                        name="mlsIdCode"
                        label="MLS Name"
                        options={mlsOptions}
                      />
                    )}
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={5} className="small padding top">
                    <WatcherMlsNum />
                    <MyTextInput
                      name="mlsNumbers[0]"
                      label="MLS #"
                      value={values.mlsNumbers?.[0] || ""}
                    />
                  </Grid.Column>
                  <Grid.Column mobile={16} computer={4} className="small padding top">
                    <Button
                      primary
                      loading={importMlsStatus === "Loading"}
                      disabled={importMlsStatus === "Loading" || importMlsStatus === "Complete"}
                      className={isMobile ? "fluid" : null}
                      style={{ marginTop: "20px" }}
                      onClick={(e) => {
                        e.preventDefault();
                        handleMlsImport(values);
                      }}
                    >
                      {importMlsStatus === "Initial" || importMlsStatus === "Error"
                        ? "Import MLS Details"
                        : `Import ${importMlsStatus}`}
                    </Button>
                  </Grid.Column>
                </Grid.Row>
                
                {!currentUserProfile.mlsAccess && importMlsStatus === "Error" && (
                  <Grid.Row>
                    <Grid.Column mobile={16} computer={16}>
                      <Message negative>
                        <Message.Header>
                          You do not have access to any MLS, and we could not find
                          that MLS number on the internet for your area.
                        </Message.Header>
                        <p>
                          Please contact your administrator to add MLS access for your
                          account, or try again with a different MLS number.
                        </p>
                      </Message>
                    </Grid.Column>
                  </Grid.Row>
                )}

                {mlsData && (
                  <>
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <Header as="h3" color="blue">
                          Select Data to Import
                        </Header>
                        <p>Choose which fields you want to import into the form:</p>
                      </Grid.Column>
                    </Grid.Row>
                    
                    <Grid.Row>
                      {mlsData?.pic && (
                        <Grid.Column mobile={16} computer={4}>
                          <div className="large bottom margin">
                            <Image
                              bordered
                              rounded
                              size="medium"
                              wrapped
                              src={mlsData.pic}
                            />
                          </div>
                        </Grid.Column>
                      )}

                      <Grid.Column mobile={16} computer={mlsData?.pic ? 12 : 16}>
                        {(() => {
                          const availableFields = getAvailableMlsFields(mlsData);
                          const fieldsByCategory = {};

                          // Group fields by category
                          Object.keys(availableFields).forEach(key => {
                            const field = availableFields[key];
                            if (!fieldsByCategory[field.category]) {
                              fieldsByCategory[field.category] = [];
                            }
                            fieldsByCategory[field.category].push({ key, ...field });
                          });

                          return Object.keys(fieldsByCategory).map(category => (
                            <div key={category} className="large bottom margin">
                              <h4>{category}</h4>
                              {fieldsByCategory[category].map(field => (
                                <div key={field.key} style={{ marginBottom: '10px' }}>
                                  <MyCheckbox
                                    name={`importOptions.${field.key}`}
                                    label={field.label}
                                  />
                                  <p className="mini bottom margin" style={{ marginLeft: '20px', color: '#666' }}>
                                    {field.value}
                                  </p>
                                </div>
                              ))}
                            </div>
                          ));
                        })()}
                      </Grid.Column>
                    </Grid.Row>
                    
                    <Grid.Row>
                      <Grid.Column mobile={16} computer={16}>
                        <div style={{ textAlign: "right", marginTop: "20px" }}>
                          <Button
                            onClick={() => dispatch(closeModal({ modalType: "DocFillImportMlsModal" }))}
                            style={{ marginRight: "10px" }}
                          >
                            Cancel
                          </Button>
                          <Button
                            primary
                            type="submit"
                            disabled={!mlsData}
                          >
                            Import Selected Data
                          </Button>
                        </div>
                      </Grid.Column>
                    </Grid.Row>
                  </>
                )}
              </Grid>
            </Form>
          )}
        </Formik>
      </div>
    </ModalWrapper>
  );
}
